import os
import sys
import shutil
import subprocess
from pathlib import Path

def build_exe():
    print("开始构建exe文件...")
    
    # 清理之前的构建文件
    if os.path.exists("dist"):
        shutil.rmtree("dist")
    if os.path.exists("build"):
        shutil.rmtree("build")
    
    # 使用PyInstaller打包
    cmd = [
        "pyinstaller",
        "--name=批量重命名工具",
        "--windowed",
        "--icon=app_icon.png",
        "--add-data=app_icon.png;.",
        "--add-data=config.json;.",
        "main.py"
    ]
    
    subprocess.run(cmd, check=True)
    print("exe文件构建完成！")

def create_installer():
    print("开始创建安装包...")
    
    # 创建NSIS脚本
    nsis_script = """
!include "MUI2.nsh"

Name "批量重命名工具"
OutFile "批量重命名工具_安装包.exe"
InstallDir "$PROGRAMFILES\\批量重命名工具"

!insertmacro MUI_PAGE_WELCOME
!insertmacro MUI_PAGE_DIRECTORY
!insertmacro MUI_PAGE_INSTFILES
!insertmacro MUI_PAGE_FINISH

!insertmacro MUI_UNPAGE_CONFIRM
!insertmacro MUI_UNPAGE_INSTFILES

!insertmacro MUI_LANGUAGE "SimpChinese"

Section "Install"
    SetOutPath "$INSTDIR"
    File /r "dist\\批量重命名工具\\*.*"
    
    CreateDirectory "$SMPROGRAMS\\批量重命名工具"
    CreateShortCut "$SMPROGRAMS\\批量重命名工具\\批量重命名工具.lnk" "$INSTDIR\\批量重命名工具.exe"
    CreateShortCut "$DESKTOP\\批量重命名工具.lnk" "$INSTDIR\\批量重命名工具.exe"
    
    WriteUninstaller "$INSTDIR\\uninstall.exe"
    
    WriteRegStr HKLM "Software\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\批量重命名工具" "DisplayName" "批量重命名工具"
    WriteRegStr HKLM "Software\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\批量重命名工具" "UninstallString" "$INSTDIR\\uninstall.exe"
SectionEnd

Section "Uninstall"
    RMDir /r "$SMPROGRAMS\\批量重命名工具"
    Delete "$DESKTOP\\批量重命名工具.lnk"
    RMDir /r "$INSTDIR"
    
    DeleteRegKey HKLM "Software\\Microsoft\\Windows\\CurrentVersion\\Uninstall\\批量重命名工具"
SectionEnd
"""
    
    # 写入NSIS脚本
    with open("installer.nsi", "w", encoding="utf-8") as f:
        f.write(nsis_script)
    
    # 运行NSIS编译
    try:
        subprocess.run(["makensis", "installer.nsi"], check=True)
        print("安装包创建完成！")
    except FileNotFoundError:
        print("错误：未找到NSIS编译器。请确保已安装NSIS并添加到系统PATH中。")
        print("您可以从 https://nsis.sourceforge.io/Download 下载NSIS。")
        sys.exit(1)

if __name__ == "__main__":
    build_exe()
    create_installer() 