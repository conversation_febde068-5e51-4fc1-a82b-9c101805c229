import os
import subprocess
import shutil
import json
from pathlib import Path

def create_dmg():
    # 清理之前的构建文件
    if os.path.exists("dist"):
        shutil.rmtree("dist")
    if os.path.exists("build"):
        shutil.rmtree("build")
    
    # 创建必要的目录
    if not os.path.exists("logs"):
        os.makedirs("logs")
    
    # 创建默认配置文件
    default_config = {
        "last_folder": ""
    }
    with open("config.json", "w", encoding="utf-8") as f:
        json.dump(default_config, f, ensure_ascii=False, indent=2)
    
    # 使用 PyInstaller 创建应用程序
    subprocess.run([
        "pyinstaller",
        "--windowed",
        "--name", "批量重命名工具",
        "--icon", "app_icon.icns",
        "--add-data", "config.json:.",
        "--add-data", "logs:logs",
        "main.py"
    ], check=True)
    
    # 创建 DMG 文件
    app_path = "dist/批量重命名工具.app"
    dmg_path = "dist/批量重命名工具.dmg"
    
    # 创建临时目录
    temp_dir = "dist/dmg_temp"
    if os.path.exists(temp_dir):
        shutil.rmtree(temp_dir)
    os.makedirs(temp_dir)
    
    # 复制应用程序到临时目录
    shutil.copytree(app_path, os.path.join(temp_dir, "批量重命名工具.app"))
    
    # 创建 Applications 的符号链接
    os.symlink("/Applications", os.path.join(temp_dir, "Applications"))
    
    # 使用 hdiutil 创建 DMG
    subprocess.run([
        "hdiutil", "create",
        "-volname", "批量重命名工具",
        "-srcfolder", temp_dir,
        "-ov",
        "-format", "UDZO",
        dmg_path
    ], check=True)
    
    # 清理临时目录
    shutil.rmtree(temp_dir)
    
    print("DMG 文件已创建完成！")

if __name__ == "__main__":
    create_dmg() 