Stack trace:
Frame         Function      Args
0007FFFF9DE0  00021005FEBA (000210285F48, 00021026AB6E, 000000000000, 0007FFFF8CE0) msys-2.0.dll+0x1FEBA
0007FFFF9DE0  0002100467F9 (000000000000, 000000000000, 000000000000, 0007FFFFA0B8) msys-2.0.dll+0x67F9
0007FFFF9DE0  000210046832 (000210285FF9, 0007FFFF9C98, 000000000000, 000000000000) msys-2.0.dll+0x6832
0007FFFF9DE0  000210068F86 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28F86
0007FFFF9DE0  0002100690B4 (0007FFFF9DF0, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x290B4
0007FFFFA0C0  00021006A49D (0007FFFF9DF0, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A49D
End of stack trace
Loaded modules:
000100400000 bash.exe
7FFDA63C0000 ntdll.dll
7FFDA5670000 KERNEL32.DLL
7FFDA3870000 KERNELBASE.dll
7FFDA5D00000 USER32.dll
7FFDA3840000 win32u.dll
7FFDA6220000 GDI32.dll
7FFDA4040000 gdi32full.dll
7FFDA3550000 msvcp_win.dll
7FFDA3C40000 ucrtbase.dll
000210040000 msys-2.0.dll
7FFDA5ED0000 advapi32.dll
7FFDA5030000 msvcrt.dll
7FFDA5AD0000 sechost.dll
7FFDA6100000 RPCRT4.dll
7FFDA2B20000 CRYPTBASE.DLL
7FFDA3E20000 bcryptPrimitives.dll
7FFDA4380000 IMM32.DLL
