import sys
import os
import shutil
import logging
import json
from datetime import datetime
from pathlib import Path
from PySide6.QtWidgets import (QApplication, QMainWindow, QPushButton, 
                             QVBoxLayout, QWidget, QFileDialog, QLabel,
                             QProgressBar, QTextEdit, QLineEdit, QCheckBox,
                             QHBoxLayout, QGroupBox)
from PySide6.QtCore import Qt, QThread, Signal

# 版本号
VERSION = "V1.2.0"

# 配置文件路径
CONFIG_FILE = "config.json"

def get_downloads_path():
    """获取下载目录路径"""
    if sys.platform == "darwin":  # macOS
        return os.path.expanduser("~/Downloads")
    elif sys.platform == "win32":  # Windows
        return os.path.expanduser("~/Downloads")
    else:  # Linux
        return os.path.expanduser("~/Downloads")

def load_config():
    if os.path.exists(CONFIG_FILE):
        try:
            with open(CONFIG_FILE, 'r', encoding='utf-8') as f:
                return json.load(f)
        except:
            return {}
    return {}

def save_config(config):
    with open(CONFIG_FILE, 'w', encoding='utf-8') as f:
        json.dump(config, f, ensure_ascii=False, indent=2)

# 配置日志
def setup_logger():
    # 在下载目录下创建日志文件夹
    log_dir = os.path.join(get_downloads_path(), "批量重命名工具_logs")
    if not os.path.exists(log_dir):
        os.makedirs(log_dir)
    
    # 创建日志文件名，包含时间戳
    timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
    log_file = os.path.join(log_dir, f"rename_{timestamp}.log")
    
    # 配置日志格式
    log_format = '%(asctime)s - %(levelname)s - %(message)s'
    date_format = '%Y-%m-%d %H:%M:%S'
    
    # 配置日志处理器
    file_handler = logging.FileHandler(log_file, encoding='utf-8')
    file_handler.setFormatter(logging.Formatter(log_format, date_format))
    
    console_handler = logging.StreamHandler()
    console_handler.setFormatter(logging.Formatter(log_format, date_format))
    
    # 配置根日志记录器
    logger = logging.getLogger()
    logger.setLevel(logging.INFO)
    logger.addHandler(file_handler)
    logger.addHandler(console_handler)
    
    # 记录日志文件路径
    logger.info(f"日志文件保存在: {log_file}")
    
    return logger

class RenameWorker(QThread):
    progress = Signal(int)
    log = Signal(str)
    finished = Signal()
    error = Signal(str)
    total_files = Signal(int)

    def __init__(self, target_dir, new_extension, keep_original, save_to_subfolder):
        super().__init__()
        self.target_dir = target_dir
        self.new_extension = new_extension
        self.keep_original = keep_original
        self.save_to_subfolder = save_to_subfolder
        self.logger = setup_logger()

    def run(self):
        try:
            # 获取所有文件
            all_files = []
            for root, _, files in os.walk(self.target_dir):
                for file in files:
                    # 跳过隐藏文件（以.开头的文件）
                    if file.startswith('.'):
                        self.log.emit(f"跳过隐藏文件: {os.path.join(root, file)}")
                        continue
                    if root != self.target_dir:  # 排除根目录
                        all_files.append((root, file))

            total_files = len(all_files)
            self.total_files.emit(total_files)
            self.log.emit(f"找到 {total_files} 个文件需要处理")

            for index, (root, file) in enumerate(all_files, 1):
                try:
                    # 获取子文件夹名称
                    subfolder = os.path.basename(root)
                    # 获取文件名和扩展名
                    name, ext = os.path.splitext(file)
                    
                    # 如果是txt文件，修改扩展名
                    if ext.lower() == '.txt':
                        new_name = f"{subfolder}-{name}{self.new_extension}"
                    else:
                        new_name = f"{subfolder}-{file}"
                    
                    # 确定目标路径
                    if self.save_to_subfolder:
                        target_dir = root
                    else:
                        target_dir = self.target_dir
                    
                    # 构建完整路径
                    old_path = os.path.join(root, file)
                    new_path = os.path.join(target_dir, new_name)
                    
                    # 如果目标文件已存在，添加数字后缀
                    counter = 1
                    while os.path.exists(new_path):
                        name, ext = os.path.splitext(new_name)
                        new_path = os.path.join(target_dir, f"{name}_{counter}{ext}")
                        counter += 1

                    # 复制或移动文件
                    if self.keep_original:
                        shutil.copy2(old_path, new_path)
                        self.log.emit(f"复制并重命名: {old_path} -> {new_path}")
                    else:
                        shutil.move(old_path, new_path)
                        self.log.emit(f"移动并重命名: {old_path} -> {new_path}")
                    
                    # 更新进度
                    progress = int(index * 100 / total_files)
                    self.progress.emit(progress)
                    
                except Exception as e:
                    self.log.emit(f"处理文件失败 {old_path}: {str(e)}")
                    continue

            self.log.emit("所有文件处理完成！")
            self.finished.emit()
        except Exception as e:
            self.error.emit(str(e))

class MainWindow(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle(f"批量文件重命名工具 {VERSION}")
        self.setMinimumSize(800, 600)
        
        # 加载配置
        self.config = load_config()
        
        # 创建主窗口部件
        main_widget = QWidget()
        self.setCentralWidget(main_widget)
        
        # 创建布局
        layout = QVBoxLayout()
        main_widget.setLayout(layout)
        
        # 创建文件夹选择区域
        folder_group = QGroupBox("文件夹选择")
        folder_layout = QVBoxLayout()
        self.folder_label = QLabel("未选择文件夹")
        self.select_button = QPushButton("选择文件夹")
        self.select_button.clicked.connect(self.select_folder)
        folder_layout.addWidget(self.folder_label)
        folder_layout.addWidget(self.select_button)
        folder_group.setLayout(folder_layout)
        layout.addWidget(folder_group)
        
        # 创建选项区域
        options_group = QGroupBox("重命名选项")
        options_layout = QVBoxLayout()
        
        # 扩展名设置
        ext_layout = QHBoxLayout()
        ext_layout.addWidget(QLabel("新扩展名:"))
        self.extension_input = QLineEdit(".webp")
        ext_layout.addWidget(self.extension_input)
        options_layout.addLayout(ext_layout)
        
        # 保留原文件选项
        self.keep_original = QCheckBox("保留原文件")
        self.keep_original.setChecked(True)
        options_layout.addWidget(self.keep_original)
        
        # 保存位置选项
        self.save_to_subfolder = QCheckBox("保存到原文件夹")
        options_layout.addWidget(self.save_to_subfolder)
        
        options_group.setLayout(options_layout)
        layout.addWidget(options_group)
        
        # 创建进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        layout.addWidget(self.progress_bar)
        
        # 创建日志显示区域
        log_group = QGroupBox("日志")
        log_layout = QVBoxLayout()
        self.log_text = QTextEdit()
        self.log_text.setReadOnly(True)
        log_layout.addWidget(self.log_text)
        log_group.setLayout(log_layout)
        layout.addWidget(log_group)
        
        # 创建按钮区域
        button_layout = QHBoxLayout()
        
        # 开始按钮
        self.start_button = QPushButton("开始重命名")
        self.start_button.clicked.connect(self.start_rename)
        self.start_button.setEnabled(False)
        button_layout.addWidget(self.start_button)
        
        # 清空按钮
        self.clear_button = QPushButton("清空日志")
        self.clear_button.clicked.connect(self.clear_log)
        button_layout.addWidget(self.clear_button)
        
        # 退出按钮
        self.exit_button = QPushButton("退出")
        self.exit_button.clicked.connect(self.close)
        button_layout.addWidget(self.exit_button)
        
        layout.addLayout(button_layout)
        
        self.target_dir = None
        self.worker = None
        
        # 如果有上次选择的文件夹，自动加载
        if 'last_folder' in self.config and os.path.exists(self.config['last_folder']):
            self.target_dir = self.config['last_folder']
            self.folder_label.setText(f"已选择: {self.target_dir}")
            self.start_button.setEnabled(True)
            self.log_text.append(f"已加载上次选择的文件夹: {self.target_dir}")

    def select_folder(self):
        folder = QFileDialog.getExistingDirectory(self, "选择目标文件夹", self.target_dir)
        if folder:
            self.target_dir = folder
            self.folder_label.setText(f"已选择: {folder}")
            self.start_button.setEnabled(True)
            self.log_text.clear()
            self.log_text.append(f"已选择文件夹: {folder}")
            
            # 保存选择的文件夹到配置
            self.config['last_folder'] = folder
            save_config(self.config)

    def clear_log(self):
        self.log_text.clear()
        self.progress_bar.setValue(0)
        self.progress_bar.setVisible(False)

    def start_rename(self):
        if not self.target_dir:
            return

        self.select_button.setEnabled(False)
        self.start_button.setEnabled(False)
        self.progress_bar.setVisible(True)
        self.progress_bar.setValue(0)
        self.log_text.clear()

        # 获取新扩展名
        new_extension = self.extension_input.text().strip()
        if not new_extension.startswith('.'):
            new_extension = '.' + new_extension

        self.worker = RenameWorker(
            self.target_dir,
            new_extension,
            self.keep_original.isChecked(),
            self.save_to_subfolder.isChecked()
        )
        self.worker.progress.connect(self.update_progress)
        self.worker.log.connect(self.update_log)
        self.worker.finished.connect(self.rename_finished)
        self.worker.error.connect(self.show_error)
        self.worker.total_files.connect(self.update_total_files)
        self.worker.start()

    def update_progress(self, value):
        self.progress_bar.setValue(value)

    def update_log(self, message):
        self.log_text.append(message)

    def update_total_files(self, total):
        self.log_text.append(f"总共需要处理 {total} 个文件")

    def rename_finished(self):
        self.progress_bar.setValue(100)
        self.select_button.setEnabled(True)
        self.start_button.setEnabled(True)
        self.log_text.append("处理完成！")

    def show_error(self, error_msg):
        self.log_text.append(f"错误: {error_msg}")
        self.select_button.setEnabled(True)
        self.start_button.setEnabled(True)
        self.progress_bar.setVisible(False)

    def closeEvent(self, event):
        # 保存配置
        save_config(self.config)
        event.accept()

if __name__ == "__main__":
    app = QApplication(sys.argv)
    window = MainWindow()
    window.show()
    sys.exit(app.exec()) 