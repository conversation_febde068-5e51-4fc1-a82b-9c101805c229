# 批量文件重命名工具

这是一个用于批量重命名文件的工具，具有以下功能：

1. 选择目标文件夹（包含子文件夹）
2. 自动扫描并重命名所有文件
3. 将重命名后的文件移动到目标文件夹根目录

## 安装要求

- Python 3.8+
- PySide6
- PyInstaller (用于打包)

## 安装步骤

1. 克隆此仓库
2. 安装依赖：
   ```bash
   pip install -r requirements.txt
   ```

## 使用方法

1. 运行程序：
   ```bash
   python main.py
   ```
2. 点击"选择文件夹"按钮选择目标文件夹
3. 点击"开始重命名"按钮执行重命名操作

## 打包说明

使用 PyInstaller 打包：
```bash
pyinstaller --windowed --name "批量重命名工具" main.py
``` 